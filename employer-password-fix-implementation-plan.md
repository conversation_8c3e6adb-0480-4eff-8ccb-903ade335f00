# Employer Password Settings Authentication Fix - Implementation Plan

## Problem Analysis

### Root Cause
The employer settings password section incorrectly renders the `CandidateSettings` component instead of a proper employer-specific password component. This causes:

1. **Authentication Token Mismatch**: CandidateSettings expects `candId` cookie but employer uses `empId` and `employerId`
2. **Wrong API Calls**: CandidateSettings makes API calls expecting candidate authentication
3. **Redirect to Wrong Sign-in**: When authentication fails, it redirects to candidate sign-in instead of employer sign-in

### Current Authentication Flow
- **Employer Login**: Sets `jstoken`, `role`, `empId`, `employerId` cookies
- **Employer Layout**: Checks `jstoken` + `role === "employer"` for authentication
- **ManageCredits**: Uses `employerId` for API calls but renders `CandidateSettings` for password section

## Implementation Plan

### Phase 1: Create Employer Password Settings Component
**File**: `visume-ui/src/views/employer/settings/EmployerPasswordSettings.jsx`

**Features**:
- Mirror candidate password change UI/UX exactly
- Use employer authentication tokens (`empId`, `employerId`)
- Call existing `/api/v1/changePassword` endpoint with `emp_id` parameter
- Handle employer-specific error states and redirects
- Maintain same validation rules (8+ characters, passwords match, etc.)

**Key Differences from CandidateSettings**:
- Use `Cookies.get("employerId")` instead of `Cookies.get("candId")`
- Send `emp_id` instead of `cand_id` in API request
- Redirect to `/employer/signin` on authentication failure
- Use employer-specific error handling

### Phase 2: Update ManageCredits Component
**File**: `visume-ui/src/views/employer/settings/ManageCredits.jsx`

**Changes**:
- Remove `CandidateSettings` import (line 9)
- Add `EmployerPasswordSettings` import
- Replace `<CandidateSettings />` with `<EmployerPasswordSettings />` in password section (line 94)

### Phase 3: Verify API Compatibility
**Endpoint**: `/api/v1/changePassword` in `userController.js`

**Current Support**:
- Already supports both `cand_id` and `emp_id` parameters
- Handles employer authentication correctly
- Returns appropriate error messages

**Verification Points**:
- Employer ID lookup works correctly
- Password validation functions properly
- JWT token handling is consistent

### Phase 4: Testing & Validation
**Test Cases**:
1. Navigate to `/employer/settings/password` - should show password form
2. Submit with invalid current password - should show error
3. Submit with mismatched new passwords - should show validation error
4. Submit with valid data - should update password successfully
5. Verify no redirects to candidate sign-in occur
6. Test authentication persistence throughout process

## Technical Implementation Details

### Authentication Token Usage
```javascript
// Current (incorrect in CandidateSettings)
const cand_id = Cookies.get("candId");

// Correct for employer
const emp_id = Cookies.get("employerId");
```

### API Request Structure
```javascript
// Employer password change request
const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`, {
  method: "PUT",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    emp_id: employerId,  // Use employer ID
    password: oldPassword,
    newPassword: newPassword,
  }),
});
```

### Error Handling
```javascript
// Redirect to employer sign-in on auth failure
if (!employerId) {
  toast.error("No Token Found, Please Login Again");
  navigate("/employer/signin");
  return;
}
```

## Files to Modify

1. **NEW**: `visume-ui/src/views/employer/settings/EmployerPasswordSettings.jsx`
2. **MODIFY**: `visume-ui/src/views/employer/settings/ManageCredits.jsx`

## Constraints & Requirements

- **Preserve Existing Functionality**: No changes to candidate password settings
- **Maintain UI/UX Consistency**: Use same design patterns as candidate settings
- **JWT Authentication**: Preserve existing token handling mechanisms
- **API Compatibility**: Use existing changePassword endpoint without modifications
- **Error Handling**: Proper employer-specific error messages and redirects

## Success Criteria

1. Employer can access password settings without authentication errors
2. Password change form displays correctly with employer branding
3. Form validation works identically to candidate version
4. API calls succeed with employer authentication
5. No redirects to candidate sign-in page occur
6. Success/error messages are appropriate for employer context
7. All existing employer functionality remains intact

## Risk Mitigation

- **Backup Plan**: If issues arise, can quickly revert ManageCredits to previous state
- **Incremental Testing**: Test each component individually before integration
- **Authentication Verification**: Verify token handling at each step
- **API Testing**: Confirm backend compatibility before frontend changes
